using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 背景滚动控制器
/// </summary>
public class BackgroundScroller : MonoBehaviour
{
    [SerializeField] private RawImage _image; // RawImage组件引用
    [SerializeField] private float _x = 0.1f; // X轴滚动速度
    [SerializeField] private float _y = 0.05f; // Y轴滚动速度
    [SerializeField] private bool _isScrolling = true; // 是否启用滚动

    private Rect _currentUvRect; // 缓存当前UV矩形
    private Vector2 _scrollSpeed; // 缓存滚动速度向量
    private bool _hasValidImage; // 是否有有效的图像组件

    private void Awake()
    {
        // 使用TryGetComponent获取组件
        if (_image == null)
        {
            _hasValidImage = TryGetComponent(out _image);
        }
        else
        {
            _hasValidImage = _image != null;
        }

        // 初始化缓存变量
        if (_hasValidImage)
        {
            _currentUvRect = _image.uvRect;
        }

        UpdateScrollSpeed();
    }

    private void Update()
    {
        // 检查是否需要滚动
        if (!_isScrolling || !_hasValidImage || (_x == 0 && _y == 0))
            return;

        // 更新UV坐标
        _currentUvRect.x += _scrollSpeed.x * Time.deltaTime;
        _currentUvRect.y += _scrollSpeed.y * Time.deltaTime;

        // 可选：添加边界控制，防止UV值过大
        _currentUvRect.x = _currentUvRect.x % 1f;
        _currentUvRect.y = _currentUvRect.y % 1f;

        // 应用更新
        _image.uvRect = _currentUvRect;
    }

    /// <summary>
    /// 更新滚动速度缓存
    /// </summary>
    private void UpdateScrollSpeed()
    {
        _scrollSpeed = new Vector2(_x, _y);
    }

    /// <summary>
    /// 设置滚动状态
    /// </summary>
    public void SetScrolling(bool isScrolling)
    {
        _isScrolling = isScrolling;
    }

    /// <summary>
    /// 设置滚动速度
    /// </summary>
    public void SetScrollSpeed(float x, float y)
    {
        _x = x;
        _y = y;
        UpdateScrollSpeed();
    }
}