using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 背景滚动控制器
/// </summary>
public class BackgroundScroller : MonoBehaviour
{
    [SerializeField] private RawImage _image; // RawImage组件引用
    [SerializeField] private float _x = 0.1f; // X轴滚动速度
    [SerializeField] private float _y = 0.05f; // Y轴滚动速度

    private void Awake()
    {
        // 如果没有手动指定，尝试获取组件
        if (_image == null)
        {
            _image = GetComponent<RawImage>();
        }
    }

    private void Update()
    {
        // 确保有RawImage组件
        if (_image != null)
        {
            // 更新UV坐标实现滚动效果
            _image.uvRect = new Rect(
                _image.uvRect.position + new Vector2(_x, _y) * Time.deltaTime, 
                _image.uvRect.size
            );
        }
    }
}